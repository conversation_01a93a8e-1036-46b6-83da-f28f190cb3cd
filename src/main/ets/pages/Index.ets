// 华为手表表盘主页面
@Entry
@Component
struct Index {
  @State currentTime: string = ''
  @State currentDate: string = ''
  @State batteryLevel: number = 85
  @State stepCount: number = 6543
  private timer: number = -1

  aboutToAppear() {
    this.updateTime()
    this.timer = setInterval(() => {
      this.updateTime()
    }, 1000)
  }

  aboutToDisappear() {
    if (this.timer !== -1) {
      clearInterval(this.timer)
    }
  }

  updateTime() {
    const now = new Date()
    this.currentTime = now.toLocaleTimeString('zh-CN', { 
      hour12: false,
      hour: '2-digit',
      minute: '2-digit'
    })
    this.currentDate = now.toLocaleDateString('zh-CN', {
      month: '2-digit',
      day: '2-digit',
      weekday: 'short'
    })
  }

  build() {
    Stack() {
      // 背景
      Circle({ width: 200, height: 200 })
        .fill('#000000')
        .stroke('#333333')
        .strokeWidth(2)

      Column() {
        // 时间显示
        Text(this.currentTime)
          .fontSize(36)
          .fontColor(Color.White)
          .fontWeight(FontWeight.Bold)
          .margin({ top: 20 })

        // 日期显示
        Text(this.currentDate)
          .fontSize(14)
          .fontColor('#CCCCCC')
          .margin({ top: 8 })

        Spacer()

        // 数据显示区域
        Row() {
          // 电池电量
          Column() {
            Text(`${this.batteryLevel}%`)
              .fontSize(12)
              .fontColor('#4CAF50')
            Text('电量')
              .fontSize(10)
              .fontColor('#888888')
          }
          .margin({ right: 20 })

          // 步数
          Column() {
            Text(`${this.stepCount}`)
              .fontSize(12)
              .fontColor('#2196F3')
            Text('步数')
              .fontSize(10)
              .fontColor('#888888')
          }
        }
        .margin({ bottom: 30 })

        // 装饰性指示器
        Row() {
          Circle({ width: 6, height: 6 })
            .fill('#FF6B6B')
          Circle({ width: 6, height: 6 })
            .fill('#4ECDC4')
            .margin({ left: 8 })
          Circle({ width: 6, height: 6 })
            .fill('#45B7D1')
            .margin({ left: 8 })
        }
        .margin({ bottom: 20 })
      }
      .width('100%')
      .height('100%')
      .justifyContent(FlexAlign.Center)
      .alignItems(HorizontalAlign.Center)
    }
    .width('100%')
    .height('100%')
    .backgroundColor('#000000')
  }
}
