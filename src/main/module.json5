{"module": {"name": "entry", "type": "entry", "description": "$string:module_desc", "mainElement": "EntryAbility", "deviceTypes": ["wearable"], "deliveryWithInstall": true, "installationFree": false, "pages": "$profile:main_pages", "abilities": [{"name": "EntryAbility", "srcEntry": "./ets/entryability/EntryAbility.ts", "description": "$string:EntryAbility_desc", "icon": "$media:icon", "label": "$string:EntryAbility_label", "startWindowIcon": "$media:icon", "startWindowBackground": "$color:start_window_background", "exported": true, "skills": [{"entities": ["entity.system.home"], "actions": ["action.system.home"]}]}], "extensionAbilities": [{"name": "WatchFaceExtensionAbility", "srcEntry": "./ets/watchfaceextensionability/WatchFaceExtensionAbility.ts", "type": "wallpaper", "exported": true, "metadata": [{"name": "ohos.extension.wallpaper", "resource": "$profile:watchface_config"}]}]}}