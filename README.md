# 华为手表表盘项目

这是一个基于HarmonyOS开发的华为手表表盘项目。

## 项目结构

```
huawei/
├── app.json5                    # 应用配置文件
├── build-profile.json5          # 构建配置文件
├── src/
│   └── main/
│       ├── ets/
│       │   ├── pages/
│       │   │   └── Index.ets    # 主页面
│       │   ├── entryability/
│       │   │   └── EntryAbility.ts
│       │   └── watchfaceextensionability/
│       │       └── WatchFaceExtensionAbility.ts
│       ├── module.json5         # 模块配置文件
│       └── resources/
│           └── base/
│               ├── element/
│               │   └── string.json
│               ├── media/       # 图片资源
│               └── profile/
│                   └── main_pages.json
└── README.md
```

## 功能特性

- 实时时间显示
- 日期显示
- 电池电量显示
- 步数显示
- 简约圆形设计
- 装饰性指示器

## 开发环境

- DevEco Studio 4.0+
- HarmonyOS SDK API 9+
- 支持华为手表设备

## 安装和运行

1. 使用DevEco Studio打开项目
2. 连接华为手表设备或使用模拟器
3. 点击运行按钮进行安装和调试

## 自定义

可以通过修改以下文件来自定义表盘：

- `src/main/ets/pages/Index.ets` - 修改表盘UI和布局
- `src/main/resources/base/element/` - 修改颜色和字符串资源
- `src/main/resources/base/media/` - 添加自定义图片资源

## 许可证

MIT License
